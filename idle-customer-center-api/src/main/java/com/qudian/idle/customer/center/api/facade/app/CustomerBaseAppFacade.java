package com.qudian.idle.customer.center.api.facade.app;

import com.qudian.idle.customer.center.api.vo.request.base.CustomerEditReqVO;
import com.qudian.idle.customer.center.api.vo.request.base.CustomerShowReqVO;
import com.qudian.idle.customer.center.api.vo.response.base.CustomerShowRespVO;
import com.qudian.idle.customer.center.api.vo.share.IdRequestVO;
import com.qudian.lme.common.dto.BaseResponseDTO;
import com.qudian.pdt.api.toolkit.vo.response.BaseResponseVO;

/**
 * <p>文件名称:com.qudian.idle.customer.center.api.facade.app.CustomerBaseAppFacade</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/12
 */
public interface CustomerBaseAppFacade {

    BaseResponseDTO<IdRequestVO> addOrEdit(CustomerEditReqVO reqVO);

    BaseResponseDTO<CustomerShowRespVO> show(CustomerShowReqVO reqVO);
}
