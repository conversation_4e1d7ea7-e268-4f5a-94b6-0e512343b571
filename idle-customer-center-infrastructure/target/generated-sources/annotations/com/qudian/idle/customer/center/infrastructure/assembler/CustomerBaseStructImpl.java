package com.qudian.idle.customer.center.infrastructure.assembler;

import com.qudian.idle.customer.center.api.vo.OptionValues;
import com.qudian.idle.customer.center.api.vo.request.base.CustomerEditReqVO;
import com.qudian.idle.customer.center.api.vo.response.CustomerPropertyVO;
import com.qudian.idle.customer.center.api.vo.response.base.CustomerShowRespVO;
import com.qudian.idle.customer.center.api.vo.response.inside.CustomerInfoInHomeRespVO;
import com.qudian.idle.customer.center.api.vo.response.mis.CustomerMisPageRespVO;
import com.qudian.idle.customer.center.infrastructure.repository.database.po.base.CustomerBasePO;
import java.time.format.DateTimeFormatter;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-16T11:53:16+0800",
    comments = "version: 1.5.5.Final, compiler: java<PERSON>, environment: Java 17 (Oracle Corporation)"
)
@Component
public class CustomerBaseStructImpl implements CustomerBaseStruct {

    private final DateTimeFormatter dateTimeFormatter_yyyy年MM月dd日_HH_mm_11363607979 = DateTimeFormatter.ofPattern( "yyyy年MM月dd日 HH:mm" );

    @Override
    public CustomerBasePO baseVO2PO(CustomerEditReqVO.CustomerEditBaseReqVO reqVO) {
        if ( reqVO == null ) {
            return null;
        }

        CustomerBasePO customerBasePO = new CustomerBasePO();

        customerBasePO.setGender( singleCode( reqVO.getGender() ) );
        customerBasePO.setAgeGroup( singleCode( reqVO.getAgeGroup() ) );
        customerBasePO.setSource( singleCode( reqVO.getSource() ) );
        customerBasePO.setHomeVisitStatus( singleCode( reqVO.getHomeVisitStatus() ) );
        customerBasePO.setOutputIntention( singleCode( reqVOMembershipOutputIntention( reqVO ) ) );
        customerBasePO.setMembershipType( singleCode( reqVOMembershipMembershipType( reqVO ) ) );
        customerBasePO.setPurchaseIntention( singleCode( reqVOMembershipPurchaseIntention( reqVO ) ) );
        customerBasePO.setProvince( reqVOAddressProvince( reqVO ) );
        customerBasePO.setCity( reqVOAddressCity( reqVO ) );
        customerBasePO.setDistrict( reqVOAddressDistrict( reqVO ) );
        customerBasePO.setDetailAddress( reqVOAddressDetailAddress( reqVO ) );
        customerBasePO.setId( reqVO.getId() );
        customerBasePO.setName( reqVO.getName() );
        customerBasePO.setMobile( reqVO.getMobile() );
        customerBasePO.setWechatId( reqVO.getWechatId() );
        customerBasePO.setType( reqVO.getType() );
        customerBasePO.setRemark( reqVO.getRemark() );
        customerBasePO.setImages( reqVO.getImages() );

        return customerBasePO;
    }

    @Override
    public CustomerShowRespVO.CustomerShowBaseRespVO PO2BaseVO(CustomerBasePO po, CustomerPropertyVO propertyVO) {
        if ( po == null ) {
            return null;
        }

        CustomerShowRespVO.CustomerShowBaseRespVO customerShowBaseRespVO = new CustomerShowRespVO.CustomerShowBaseRespVO();

        customerShowBaseRespVO.setMembership( customerBasePOToCustomerShowMembershipReqVO( po, propertyVO ) );
        customerShowBaseRespVO.setAddress( customerBasePOToCustomerShowAddressReqVO( po, propertyVO ) );
        customerShowBaseRespVO.setGender( genderOptionValues( po.getGender(), propertyVO ) );
        customerShowBaseRespVO.setAgeGroup( ageGroupOptionValues( po.getAgeGroup(), propertyVO ) );
        customerShowBaseRespVO.setSource( sourceModeOptionValues( po.getSource(), propertyVO ) );
        customerShowBaseRespVO.setHomeVisitStatus( homeVisitStatusOptionValues( po.getHomeVisitStatus(), propertyVO ) );
        customerShowBaseRespVO.setId( po.getId() );
        customerShowBaseRespVO.setName( po.getName() );
        customerShowBaseRespVO.setMobile( po.getMobile() );
        customerShowBaseRespVO.setType( po.getType() );
        customerShowBaseRespVO.setWechatId( po.getWechatId() );
        customerShowBaseRespVO.setRemark( po.getRemark() );
        customerShowBaseRespVO.setImages( po.getImages() );

        return customerShowBaseRespVO;
    }

    @Override
    public CustomerInfoInHomeRespVO PO2HomeBatchVO(CustomerBasePO po, CustomerPropertyVO propertyVO) {
        if ( po == null ) {
            return null;
        }

        CustomerInfoInHomeRespVO customerInfoInHomeRespVO = new CustomerInfoInHomeRespVO();

        customerInfoInHomeRespVO.setGender( genderOptionValues( po.getGender(), propertyVO ) );
        customerInfoInHomeRespVO.setAgeGroup( ageGroupOptionValues( po.getAgeGroup(), propertyVO ) );
        customerInfoInHomeRespVO.setOutputIntention( outputIntentionOptionValues( po.getOutputIntention(), propertyVO ) );
        customerInfoInHomeRespVO.setMembershipType( membershipTypeOptionValues( po.getMembershipType(), propertyVO ) );
        customerInfoInHomeRespVO.setPurchaseIntention( purchaseIntentionOptionValues( po.getPurchaseIntention(), propertyVO ) );
        customerInfoInHomeRespVO.setId( po.getId() );
        customerInfoInHomeRespVO.setName( po.getName() );
        customerInfoInHomeRespVO.setMobile( po.getMobile() );
        customerInfoInHomeRespVO.setWechatId( po.getWechatId() );

        return customerInfoInHomeRespVO;
    }

    @Override
    public CustomerMisPageRespVO PO2MisPageVO(CustomerBasePO po, CustomerPropertyVO propertyVO) {
        if ( po == null ) {
            return null;
        }

        CustomerMisPageRespVO customerMisPageRespVO = new CustomerMisPageRespVO();

        customerMisPageRespVO.setSource( sourceModeToString( po.getSource(), propertyVO ) );
        customerMisPageRespVO.setOutputIntention( outputIntentionToString( po.getOutputIntention(), propertyVO ) );
        customerMisPageRespVO.setMembershipType( membershipTypeToString( po.getMembershipType(), propertyVO ) );
        customerMisPageRespVO.setPurchaseIntention( purchaseIntentionToString( po.getPurchaseIntention(), propertyVO ) );
        customerMisPageRespVO.setManagerId( po.getRelatedManagerId() );
        customerMisPageRespVO.setManagerName( po.getRelatedManagerName() );
        if ( po.getCreatedTime() != null ) {
            customerMisPageRespVO.setCreatedTime( dateTimeFormatter_yyyy年MM月dd日_HH_mm_11363607979.format( po.getCreatedTime() ) );
        }
        customerMisPageRespVO.setId( po.getId() );
        customerMisPageRespVO.setName( po.getName() );
        customerMisPageRespVO.setMobile( po.getMobile() );

        return customerMisPageRespVO;
    }

    private OptionValues reqVOMembershipOutputIntention(CustomerEditReqVO.CustomerEditBaseReqVO customerEditBaseReqVO) {
        if ( customerEditBaseReqVO == null ) {
            return null;
        }
        CustomerEditReqVO.CustomerEditMembershipReqVO membership = customerEditBaseReqVO.getMembership();
        if ( membership == null ) {
            return null;
        }
        OptionValues outputIntention = membership.getOutputIntention();
        if ( outputIntention == null ) {
            return null;
        }
        return outputIntention;
    }

    private OptionValues reqVOMembershipMembershipType(CustomerEditReqVO.CustomerEditBaseReqVO customerEditBaseReqVO) {
        if ( customerEditBaseReqVO == null ) {
            return null;
        }
        CustomerEditReqVO.CustomerEditMembershipReqVO membership = customerEditBaseReqVO.getMembership();
        if ( membership == null ) {
            return null;
        }
        OptionValues membershipType = membership.getMembershipType();
        if ( membershipType == null ) {
            return null;
        }
        return membershipType;
    }

    private OptionValues reqVOMembershipPurchaseIntention(CustomerEditReqVO.CustomerEditBaseReqVO customerEditBaseReqVO) {
        if ( customerEditBaseReqVO == null ) {
            return null;
        }
        CustomerEditReqVO.CustomerEditMembershipReqVO membership = customerEditBaseReqVO.getMembership();
        if ( membership == null ) {
            return null;
        }
        OptionValues purchaseIntention = membership.getPurchaseIntention();
        if ( purchaseIntention == null ) {
            return null;
        }
        return purchaseIntention;
    }

    private String reqVOAddressProvince(CustomerEditReqVO.CustomerEditBaseReqVO customerEditBaseReqVO) {
        if ( customerEditBaseReqVO == null ) {
            return null;
        }
        CustomerEditReqVO.CustomerEditAddressReqVO address = customerEditBaseReqVO.getAddress();
        if ( address == null ) {
            return null;
        }
        String province = address.getProvince();
        if ( province == null ) {
            return null;
        }
        return province;
    }

    private String reqVOAddressCity(CustomerEditReqVO.CustomerEditBaseReqVO customerEditBaseReqVO) {
        if ( customerEditBaseReqVO == null ) {
            return null;
        }
        CustomerEditReqVO.CustomerEditAddressReqVO address = customerEditBaseReqVO.getAddress();
        if ( address == null ) {
            return null;
        }
        String city = address.getCity();
        if ( city == null ) {
            return null;
        }
        return city;
    }

    private String reqVOAddressDistrict(CustomerEditReqVO.CustomerEditBaseReqVO customerEditBaseReqVO) {
        if ( customerEditBaseReqVO == null ) {
            return null;
        }
        CustomerEditReqVO.CustomerEditAddressReqVO address = customerEditBaseReqVO.getAddress();
        if ( address == null ) {
            return null;
        }
        String district = address.getDistrict();
        if ( district == null ) {
            return null;
        }
        return district;
    }

    private String reqVOAddressDetailAddress(CustomerEditReqVO.CustomerEditBaseReqVO customerEditBaseReqVO) {
        if ( customerEditBaseReqVO == null ) {
            return null;
        }
        CustomerEditReqVO.CustomerEditAddressReqVO address = customerEditBaseReqVO.getAddress();
        if ( address == null ) {
            return null;
        }
        String detailAddress = address.getDetailAddress();
        if ( detailAddress == null ) {
            return null;
        }
        return detailAddress;
    }

    protected CustomerShowRespVO.CustomerShowMembershipReqVO customerBasePOToCustomerShowMembershipReqVO(CustomerBasePO customerBasePO, CustomerPropertyVO propertyVO) {
        if ( customerBasePO == null ) {
            return null;
        }

        CustomerShowRespVO.CustomerShowMembershipReqVO customerShowMembershipReqVO = new CustomerShowRespVO.CustomerShowMembershipReqVO();

        customerShowMembershipReqVO.setOutputIntention( outputIntentionOptionValues( customerBasePO.getOutputIntention(), propertyVO ) );
        customerShowMembershipReqVO.setMembershipType( membershipTypeOptionValues( customerBasePO.getMembershipType(), propertyVO ) );
        customerShowMembershipReqVO.setPurchaseIntention( purchaseIntentionOptionValues( customerBasePO.getPurchaseIntention(), propertyVO ) );

        return customerShowMembershipReqVO;
    }

    protected CustomerShowRespVO.CustomerShowAddressReqVO customerBasePOToCustomerShowAddressReqVO(CustomerBasePO customerBasePO, CustomerPropertyVO propertyVO) {
        if ( customerBasePO == null ) {
            return null;
        }

        CustomerShowRespVO.CustomerShowAddressReqVO customerShowAddressReqVO = new CustomerShowRespVO.CustomerShowAddressReqVO();

        customerShowAddressReqVO.setProvince( customerBasePO.getProvince() );
        customerShowAddressReqVO.setCity( customerBasePO.getCity() );
        customerShowAddressReqVO.setDistrict( customerBasePO.getDistrict() );
        customerShowAddressReqVO.setDetailAddress( customerBasePO.getDetailAddress() );

        return customerShowAddressReqVO;
    }
}
