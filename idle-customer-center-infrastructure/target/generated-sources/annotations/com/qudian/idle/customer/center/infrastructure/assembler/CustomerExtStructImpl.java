package com.qudian.idle.customer.center.infrastructure.assembler;

import com.qudian.idle.customer.center.api.vo.request.base.CustomerEditReqVO;
import com.qudian.idle.customer.center.infrastructure.repository.database.po.base.CustomerExtPO;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-16T10:36:55+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17 (Oracle Corporation)"
)
@Component
public class CustomerExtStructImpl implements CustomerExtStruct {

    @Override
    public CustomerExtPO preVO2PO(CustomerEditReqVO.CustomerEditExtReqVO vo) {
        if ( vo == null ) {
            return null;
        }

        CustomerExtPO customerExtPO = new CustomerExtPO();

        return customerExtPO;
    }
}
