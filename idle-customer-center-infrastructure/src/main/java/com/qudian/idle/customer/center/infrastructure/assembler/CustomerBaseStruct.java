package com.qudian.idle.customer.center.infrastructure.assembler;

import com.qudian.idle.customer.center.api.vo.OptionValues;
import com.qudian.idle.customer.center.api.vo.request.base.CustomerEditReqVO;
import com.qudian.idle.customer.center.api.vo.response.CustomerPropertyVO;
import com.qudian.idle.customer.center.api.vo.response.base.CustomerShowRespVO;
import com.qudian.idle.customer.center.api.vo.response.inside.CustomerInfoInHomeRespVO;
import com.qudian.idle.customer.center.api.vo.response.mis.CustomerMisPageRespVO;
import com.qudian.idle.customer.center.api.vo.share.Property;
import com.qudian.idle.customer.center.infrastructure.repository.database.po.base.CustomerBasePO;
import org.mapstruct.*;

import java.util.List;

/**
 * <p>文件名称:com.qudian.idle.customer.center.infrastructure.assembler.CustomerBaseVO2PO</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/12
 */
@Mapper
public interface CustomerBaseStruct {

    @Mappings({
            @Mapping(source = "gender", target = "gender", qualifiedByName = "singleCode"),
            @Mapping(source = "ageGroup", target = "ageGroup", qualifiedByName = "singleCode"),
            @Mapping(source = "source", target = "source", qualifiedByName = "singleCode"),
            @Mapping(source = "homeVisitStatus", target = "homeVisitStatus", qualifiedByName = "singleCode"),
            @Mapping(source = "membership.outputIntention", target = "outputIntention", qualifiedByName = "singleCode"),
            @Mapping(source = "membership.membershipType", target = "membershipType", qualifiedByName = "singleCode"),
            @Mapping(source = "membership.purchaseIntention", target = "purchaseIntention", qualifiedByName = "singleCode"),
            @Mapping(source = "address.province", target = "province"),
            @Mapping(source = "address.city", target = "city"),
            @Mapping(source = "address.district", target = "district"),
            @Mapping(source = "address.detailAddress", target = "detailAddress")
    })
    CustomerBasePO baseVO2PO(CustomerEditReqVO.CustomerEditBaseReqVO reqVO);

    @Mappings({
            @Mapping(source = "po.gender", target = "gender", qualifiedByName = "genderOptionValues"),
            @Mapping(source = "po.ageGroup", target = "ageGroup", qualifiedByName = "ageGroupOptionValues"),
            @Mapping(source = "po.source", target = "source", qualifiedByName = "sourceModeOptionValues"),
            @Mapping(source = "po.outputIntention", target = "membership.outputIntention", qualifiedByName = "outputIntentionOptionValues"),
            @Mapping(source = "po.homeVisitStatus", target = "homeVisitStatus", qualifiedByName = "homeVisitStatusOptionValues"),
            @Mapping(source = "po.membershipType", target = "membership.membershipType", qualifiedByName = "membershipTypeOptionValues"),
            @Mapping(source = "po.purchaseIntention", target = "membership.purchaseIntention", qualifiedByName = "purchaseIntentionOptionValues"),
            @Mapping(source = "po.province", target = "address.province"),
            @Mapping(source = "po.city", target = "address.city"),
            @Mapping(source = "po.district", target = "address.district"),
            @Mapping(source = "po.detailAddress", target = "address.detailAddress")
    })
    CustomerShowRespVO.CustomerShowBaseRespVO PO2BaseVO(CustomerBasePO po,  @Context CustomerPropertyVO propertyVO);

    @Mappings({
            @Mapping(source = "po.gender", target = "gender", qualifiedByName = "genderOptionValues"),
            @Mapping(source = "po.ageGroup", target = "ageGroup", qualifiedByName = "ageGroupOptionValues"),
            @Mapping(source = "po.outputIntention", target = "outputIntention", qualifiedByName = "outputIntentionOptionValues"),
            @Mapping(source = "po.membershipType", target = "membershipType", qualifiedByName = "membershipTypeOptionValues"),
            @Mapping(source = "po.purchaseIntention", target = "purchaseIntention", qualifiedByName = "purchaseIntentionOptionValues"),
    })
    CustomerInfoInHomeRespVO PO2HomeBatchVO(CustomerBasePO po, @Context CustomerPropertyVO propertyVO);

    @Mappings({
            @Mapping(source = "po.source", target = "source", qualifiedByName = "sourceModeToString"),
            @Mapping(source = "po.outputIntention", target = "outputIntention", qualifiedByName = "outputIntentionToString"),
            @Mapping(source = "po.membershipType", target = "membershipType", qualifiedByName = "membershipTypeToString"),
            @Mapping(source = "po.purchaseIntention", target = "purchaseIntention", qualifiedByName = "purchaseIntentionToString"),
            @Mapping(source = "po.relatedManagerId", target = "managerId"),
            @Mapping(source = "po.relatedManagerName", target = "managerName"),
            @Mapping(source = "po.createdTime", target = "createdTime", dateFormat = "yyyy年MM月dd日 HH:mm")
    })
    CustomerMisPageRespVO PO2MisPageVO(CustomerBasePO po, @Context CustomerPropertyVO propertyVO);

    @Named("sourceModeToString")
    default String sourceModeToString(Integer code, @Context CustomerPropertyVO propertyVO) {
        if (code == null) {
            return null;
        }
        return findMatchingProperty(code, propertyVO.getSource()).title();
    }
    @Named("membershipTypeToString")
    default String membershipTypeToString(Integer code, @Context CustomerPropertyVO propertyVO) {
        if (code == null) {
            return null;
        }
        return findMatchingProperty(code, propertyVO.getMembershipType()).title();
    }
    @Named("purchaseIntentionToString")
    default String purchaseIntentionToString(Integer code, @Context CustomerPropertyVO propertyVO) {
        if (code == null) {
            return null;
        }
        return findMatchingProperty(code, propertyVO.getPurchaseIntention()).title();
    }

    @Named("outputIntentionToString")
    default String outputIntentionToString(Integer code, @Context CustomerPropertyVO propertyVO) {
        if (code == null) {
            return null;
        }
        return findMatchingProperty(code, propertyVO.getOutputIntention()).title();
    }

    @Named("singleCode")
    default Integer singleCode(OptionValues v) {
        return null == v ? null : v.requireSingleCode();
    }

    @Named("genderOptionValues")
    default OptionValues genderOptionValues(Integer code, @Context CustomerPropertyVO propertyVO) {
        if (code == null) {
            return null;
        }
        return findMatchingProperty(code, propertyVO.getGender());
    }
    @Named("ageGroupOptionValues")
    default OptionValues ageGroupOptionValues(Integer code, @Context CustomerPropertyVO propertyVO) {
        if (code == null) {
            return null;
        }
        return findMatchingProperty(code, propertyVO.getAgeGroup());
    }
    @Named("sourceModeOptionValues")
    default OptionValues sourceModeOptionValues(Integer code, @Context CustomerPropertyVO propertyVO) {
        if (code == null) {
            return null;
        }
        return findMatchingProperty(code, propertyVO.getSource());
    }
    @Named("homeVisitStatusOptionValues")
    default OptionValues homeVisitStatusOptionValues(Integer code, @Context CustomerPropertyVO propertyVO) {
        if (code == null) {
            return null;
        }
        return findMatchingProperty(code, propertyVO.getHomeVisitStatus());
    }
    @Named("outputIntentionOptionValues")
    default OptionValues outputIntentionOptionValues(Integer code, @Context CustomerPropertyVO propertyVO) {
        if (code == null) {
            return null;
        }
        return findMatchingProperty(code, propertyVO.getOutputIntention());
    }
    @Named("membershipTypeOptionValues")
    default OptionValues membershipTypeOptionValues(Integer code, @Context CustomerPropertyVO propertyVO) {
        if (code == null) {
            return null;
        }
        return findMatchingProperty(code, propertyVO.getMembershipType());
    }
    @Named("purchaseIntentionOptionValues")
    default OptionValues purchaseIntentionOptionValues(Integer code, @Context CustomerPropertyVO propertyVO) {
        if (code == null) {
            return null;
        }
        return findMatchingProperty(code, propertyVO.getPurchaseIntention());
    }


    private OptionValues findMatchingProperty(Integer code, List<Property> properties) {
        if (properties == null || properties.isEmpty()) {
            return new OptionValues();
        }
        for (Property prop : properties) {
            if (code.equals(Integer.valueOf(prop.getCode()))) {
                return OptionValues.of(code, prop.getTitle());
            }
        }
        return new OptionValues();
    }
}
